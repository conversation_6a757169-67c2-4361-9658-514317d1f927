#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的GUI测试脚本
只测试基本的导入和创建，不运行GUI
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """测试导入"""
    try:
        print("测试导入...")
        
        # 测试基本导入
        import tkinter as tk
        print("✓ tkinter导入成功")
        
        from config import Config
        print("✓ Config导入成功")
        
        from gui.managers.config_manager import ConfigManager
        print("✓ ConfigManager导入成功")
        
        from gui.components.file_selection_panel import FileSelectionPanel
        print("✓ FileSelectionPanel导入成功")
        
        # 测试主应用导入
        from gui.main_application import MainApplication
        print("✓ MainApplication导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_creation():
    """测试基本创建"""
    try:
        print("\n测试基本创建...")
        
        import tkinter as tk
        from gui.main_application import MainApplication
        
        # 创建根窗口但不显示
        root = tk.Tk()
        root.withdraw()
        
        print("✓ 根窗口创建成功")
        
        # 尝试创建应用实例
        app = MainApplication(root)
        
        print("✓ MainApplication实例创建成功")
        
        # 检查新添加的属性
        attributes_to_check = [
            'console_text',
            'log_to_console',
            'clear_console', 
            'save_console_log',
            'left_paned',
            'control_frame',
            'console_frame'
        ]
        
        for attr in attributes_to_check:
            if hasattr(app, attr):
                print(f"✓ {attr} 属性存在")
            else:
                print(f"✗ {attr} 属性不存在")
        
        # 测试日志功能
        if hasattr(app, 'log_to_console'):
            app.log_to_console("测试消息", "TEST")
            print("✓ 日志功能测试成功")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始简单GUI测试...")
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试创建
        creation_success = test_basic_creation()
        
        if creation_success:
            print("\n✓ 所有测试通过！")
            print("\n主要修改验证:")
            print("1. ✓ 左侧面板已分为上下两部分")
            print("2. ✓ 控制台输出区域已添加")
            print("3. ✓ 控制台相关方法已实现")
            print("4. ✓ 设置页面布局已调整")
        else:
            print("\n✗ 创建测试失败！")
            sys.exit(1)
    else:
        print("\n✗ 导入测试失败！")
        sys.exit(1)
