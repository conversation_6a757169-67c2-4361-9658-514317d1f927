#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试控制台修改的脚本
验证主界面左下区域的控制台输出区域和设置页面右上角的保存按钮
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_console_modifications():
    """测试控制台修改"""
    try:
        # 导入主应用
        from gui.main_application import MainApplication
        
        print("✓ 成功导入MainApplication")
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口，只测试创建
        
        print("✓ 成功创建根窗口")
        
        # 创建应用实例
        app = MainApplication(root)
        
        print("✓ 成功创建MainApplication实例")
        
        # 检查控制台相关属性
        if hasattr(app, 'console_text'):
            print("✓ 控制台文本区域已创建")
        else:
            print("✗ 控制台文本区域未找到")
            
        if hasattr(app, 'log_to_console'):
            print("✓ log_to_console方法已创建")
            # 测试日志输出
            app.log_to_console("测试日志消息", "TEST")
            print("✓ 日志输出测试成功")
        else:
            print("✗ log_to_console方法未找到")
            
        if hasattr(app, 'clear_console'):
            print("✓ clear_console方法已创建")
        else:
            print("✗ clear_console方法未找到")
            
        if hasattr(app, 'save_console_log'):
            print("✓ save_console_log方法已创建")
        else:
            print("✗ save_console_log方法未找到")
        
        # 检查左侧面板结构
        if hasattr(app, 'left_paned'):
            print("✓ 左侧分栏窗口已创建")
        else:
            print("✗ 左侧分栏窗口未找到")
            
        if hasattr(app, 'control_frame'):
            print("✓ 控制框架已创建")
        else:
            print("✗ 控制框架未找到")
            
        if hasattr(app, 'console_frame'):
            print("✓ 控制台框架已创建")
        else:
            print("✗ 控制台框架未找到")
        
        # 检查设置页面结构
        if hasattr(app, 'settings_frame'):
            print("✓ 设置框架已创建")
            
            # 检查设置框架的子组件
            children = app.settings_frame.winfo_children()
            has_top_frame = False
            for child in children:
                if isinstance(child, ttk.Frame):
                    # 检查是否有保存按钮
                    buttons = [w for w in child.winfo_children() if isinstance(w, ttk.Button)]
                    for button in buttons:
                        if button.cget('text') == '保存设置':
                            print("✓ 保存设置按钮已移至顶部")
                            has_top_frame = True
                            break
                    if has_top_frame:
                        break
            
            if not has_top_frame:
                print("? 保存设置按钮位置需要进一步检查")
        else:
            print("✗ 设置框架未找到")
        
        print("\n=== 控制台修改测试完成 ===")
        print("主要修改:")
        print("1. 左侧面板分为上下两部分")
        print("2. 下部分添加了控制台输出区域")
        print("3. 设置页面的保存按钮移至右上角")
        print("4. 添加了控制台相关的功能方法")
        
        # 销毁窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试控制台修改...")
    success = test_console_modifications()
    if success:
        print("\n✓ 所有测试通过！")
    else:
        print("\n✗ 测试失败！")
        sys.exit(1)
