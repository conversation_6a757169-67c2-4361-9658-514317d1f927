#!/usr/bin/env python3
"""
测试tiktoken编码修复
验证打包后的应用程序能否正确使用tiktoken
"""

import sys
import os
import subprocess
import tempfile

def test_tiktoken_in_packaged_app():
    """测试打包应用中的tiktoken功能"""
    print("=== 测试tiktoken编码修复 ===")
    
    # 创建测试脚本
    test_script = '''
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "_internal"))

try:
    import tiktoken
    print("✓ tiktoken导入成功")
    
    # 测试获取cl100k_base编码
    encoding = tiktoken.get_encoding("cl100k_base")
    print("✓ cl100k_base编码获取成功")
    
    # 测试编码功能
    test_text = "Hello, world! 这是一个测试文本。"
    tokens = encoding.encode(test_text)
    decoded_text = encoding.decode(tokens)
    
    print(f"✓ 原始文本: {test_text}")
    print(f"✓ Token数量: {len(tokens)}")
    print(f"✓ 解码文本: {decoded_text}")
    
    if test_text == decoded_text:
        print("✓ 编码解码测试通过")
        print("SUCCESS: tiktoken功能正常")
    else:
        print("✗ 编码解码测试失败")
        print("FAILED: 编码解码不匹配")
        sys.exit(1)
        
except Exception as e:
    print(f"✗ tiktoken测试失败: {str(e)}")
    print("FAILED: tiktoken功能异常")
    sys.exit(1)
'''
    
    # 将测试脚本写入临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
        f.write(test_script)
        temp_script_path = f.name
    
    try:
        # 复制测试脚本到打包目录
        dist_script_path = os.path.join("dist", "main", "test_tiktoken.py")
        with open(temp_script_path, 'r', encoding='utf-8') as src:
            with open(dist_script_path, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
        
        print(f"测试脚本已创建: {dist_script_path}")
        
        # 在打包目录中运行测试
        result = subprocess.run(
            [sys.executable, "test_tiktoken.py"],
            cwd=os.path.join("dist", "main"),
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        print("=== 测试输出 ===")
        print(result.stdout)
        if result.stderr:
            print("=== 错误输出 ===")
            print(result.stderr)
        
        if result.returncode == 0:
            print("🎉 tiktoken修复验证成功！")
            return True
        else:
            print("❌ tiktoken修复验证失败！")
            return False
            
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_script_path)
            dist_script_path = os.path.join("dist", "main", "test_tiktoken.py")
            if os.path.exists(dist_script_path):
                os.unlink(dist_script_path)
        except:
            pass

def test_source_tiktoken():
    """测试源码环境中的tiktoken功能"""
    print("\n=== 测试源码环境tiktoken ===")
    
    try:
        import tiktoken
        print("OK tiktoken导入成功")

        encoding = tiktoken.get_encoding("cl100k_base")
        print("OK cl100k_base编码获取成功")

        test_text = "Hello, world! 这是一个测试文本。"
        tokens = encoding.encode(test_text)
        decoded_text = encoding.decode(tokens)

        print(f"OK 原始文本: {test_text}")
        print(f"OK Token数量: {len(tokens)}")
        print(f"OK 解码文本: {decoded_text}")

        if test_text == decoded_text:
            print("OK 源码环境tiktoken功能正常")
            return True
        else:
            print("ERROR 源码环境编码解码不匹配")
            return False

    except Exception as e:
        print(f"ERROR 源码环境tiktoken测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始tiktoken修复验证...")
    
    # 测试源码环境
    source_ok = test_source_tiktoken()
    
    # 测试打包环境
    packaged_ok = test_tiktoken_in_packaged_app()
    
    print("\n=== 测试总结 ===")
    print(f"源码环境: {'OK 通过' if source_ok else 'ERROR 失败'}")
    print(f"打包环境: {'OK 通过' if packaged_ok else 'ERROR 失败'}")

    if source_ok and packaged_ok:
        print("SUCCESS 所有测试通过！tiktoken修复成功！")
        sys.exit(0)
    else:
        print("ERROR 部分测试失败！")
        sys.exit(1)
