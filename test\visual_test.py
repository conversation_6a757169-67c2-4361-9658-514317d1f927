#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化测试脚本
直接运行GUI来验证修改
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_visual_test():
    """运行可视化测试"""
    try:
        import tkinter as tk
        from gui.main_application import MainApplication
        
        print("创建GUI窗口进行可视化测试...")
        print("请检查以下内容：")
        print("1. 主界面左侧是否分为上下两部分")
        print("2. 左下部分是否有'控制台输出'区域")
        print("3. 控制台区域是否有'清空'和'保存日志'按钮")
        print("4. 设置页面右上角是否有'保存设置'按钮")
        print("5. 原有功能是否正常")
        
        # 创建根窗口
        root = tk.Tk()
        
        # 创建应用
        app = MainApplication(root)
        
        # 测试控制台日志功能
        app.log_to_console("GUI测试启动", "INFO")
        app.log_to_console("控制台输出功能正常", "SUCCESS")
        app.log_to_console("这是一条测试警告", "WARNING")
        app.log_to_console("这是一条测试错误", "ERROR")
        
        # 运行GUI
        root.mainloop()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_visual_test()
