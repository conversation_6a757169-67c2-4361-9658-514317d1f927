#!/usr/bin/env python3
"""
测试所有转换功能
验证TXT、DOCX、XLSX转换是否正常工作
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from csv_to_tdocx.csv_to_txt_converter import QuizToTxtConverter
from csv_to_tdocx.csv_to_docx_converter import QuizToDocxConverter
from csv_to_tdocx.csv_to_xlsx_converter import CsvToXlsxConverter

def test_all_conversions():
    """测试所有转换功能"""
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 测试文件路径
    test_csv = "test/test_conversion.csv"
    output_dir = "test"
    
    print("=" * 50)
    print("测试所有CSV转换功能")
    print("=" * 50)
    
    # 检查测试文件是否存在
    if not os.path.exists(test_csv):
        print(f"错误: 测试文件 {test_csv} 不存在")
        return
    
    print(f"输入文件: {test_csv}")
    print(f"输出目录: {output_dir}")
    print()
    
    # 测试1: CSV转TXT
    print("1. 测试CSV转TXT...")
    try:
        txt_converter = QuizToTxtConverter()
        txt_result = txt_converter.convert(test_csv, output_dir)
        
        if txt_result.get("status") == "success":
            print(f"   ✓ TXT转换成功")
            print(f"   输出文件: {txt_result.get('output_path')}")
            print(f"   转换题目: {txt_result.get('converted_count')}")
        else:
            print(f"   ✗ TXT转换失败: {txt_result.get('message')}")
    except Exception as e:
        print(f"   ✗ TXT转换异常: {e}")
    print()
    
    # 测试2: CSV转DOCX
    print("2. 测试CSV转DOCX...")
    try:
        docx_converter = QuizToDocxConverter()
        docx_result = docx_converter.convert(test_csv, output_dir)
        
        if docx_result.get("success"):
            print(f"   ✓ DOCX转换成功")
            print(f"   输出文件: {docx_result.get('output_path')}")
            print(f"   转换题目: {docx_result.get('successful_conversions')}")
        else:
            print(f"   ✗ DOCX转换失败: {docx_result.get('message')}")
    except Exception as e:
        print(f"   ✗ DOCX转换异常: {e}")
    print()
    
    # 测试3: CSV转XLSX
    print("3. 测试CSV转XLSX...")
    try:
        xlsx_converter = CsvToXlsxConverter()
        xlsx_result = xlsx_converter.convert(test_csv, output_dir)
        
        if xlsx_result.get("success"):
            print(f"   ✓ XLSX转换成功")
            print(f"   输出文件: {xlsx_result.get('output_path')}")
            print(f"   行数: {xlsx_result.get('row_count')}")
            print(f"   列数: {xlsx_result.get('column_count')}")
            print(f"   编码: {xlsx_result.get('input_encoding')}")
        else:
            print(f"   ✗ XLSX转换失败: {xlsx_result.get('message')}")
    except Exception as e:
        print(f"   ✗ XLSX转换异常: {e}")
    print()
    
    # 检查输出文件
    print("4. 检查输出文件...")
    expected_files = [
        "test_conversion.txt",
        "test_conversion.docx", 
        "test_conversion.xlsx"
    ]
    
    for filename in expected_files:
        filepath = os.path.join(output_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"   ✓ {filename} 存在 ({size} 字节)")
        else:
            print(f"   ✗ {filename} 不存在")
    
    print()
    print("=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    test_all_conversions()
