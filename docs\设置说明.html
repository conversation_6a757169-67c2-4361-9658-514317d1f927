<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置区域使用说明</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 20px auto;
            padding: 0 15px;
            background-color: #f9f9f9;
        }
        h1, h2, h3 {
            color: #0056b3;
        }
        h1 {
            text-align: center;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h3 {
            color: #007bff;
            margin-top: 20px;
        }
        ul {
            list-style-type: disc;
            margin-left: 20px;
        }
        li {
            margin-bottom: 10px;
        }
        strong {
            color: #000;
        }
        .example {
            background-color: #e9ecef;
            border-left: 4px solid #007bff;
            padding: 10px 15px;
            margin: 10px 0;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>设置区域使用说明</h1>

    <p>本工具的“设置”标签页提供了对API连接、文本处理方式以及生成题目类型和数量的精细控制。以下是各个设置项的详细说明和使用示例：</p>

    <hr>

    <h2>一、 API 配置</h2>
    <p>此部分用于配置与大语言模型（LLM）API的连接，确保工具能够正常调用外部服务来生成题目。</p>

    <h3>1. API基础URL</h3>
    <ul>
        <li><strong>作用</strong>：指定大语言模型API的服务地址。这是工具与外部API通信的起点。</li>
        <li><div class="example"><strong>用法示例</strong>：如果您使用的是自定义的本地模型服务，或者有特定的API网关，您需要在此处填写其URL，例如 <code>https://api.your-company.com/v1</code>。对于大多数公共API服务，会有默认的URL，但如果需要连接到私有或代理服务，则需要修改。</div></li>
    </ul>

    <h3>2. API密钥</h3>
    <ul>
        <li><strong>作用</strong>：用于认证您的API请求。API密钥是您访问大语言模型服务的凭证，确保只有授权用户才能使用服务。</li>
        <li><div class="example"><strong>用法示例</strong>：当您注册大语言模型服务后，会获得一个唯一的API密钥（通常是一串长字符）。请将其粘贴到此处。为了安全，输入时会显示为星号（*）。请务必妥善保管您的API密钥，不要泄露。</div></li>
    </ul>

    <h3>3. 模型名称</h3>
    <ul>
        <li><strong>作用</strong>：指定用于生成题目的具体大语言模型。不同的模型在性能、成本和生成质量上可能有所差异。</li>
        <li><div class="example"><strong>用法示例</strong>：例如，您可以选择 <code>gpt-3.5-turbo</code> 用于快速生成，或选择 <code>gpt-4</code> 用于更高质量但可能更慢的生成。根据您的需求和API提供商支持的模型列表进行选择。</div></li>
    </ul>

    <h3>4. 温度 (Temperature)</h3>
    <ul>
        <li><strong>作用</strong>：控制生成文本的“创造性”或“随机性”。值越高，生成的内容越随机和多样；值越低，生成的内容越保守和确定。</li>
        <li><div class="example"><strong>用法示例</strong>：
            <ul>
                <li>设置为 <code>0.7</code>（默认值）：通常能生成兼具创造性和相关性的题目。</li>
                <li>设置为 <code>0.2</code>（较低值）：题目会更侧重于原文的直接提取和事实性，变化较少。适用于需要精确、保守题目的场景。</li>
                <li>设置为 <code>1.0</code>（较高值）：题目可能会出现更多意想不到的措辞或视角，甚至可能偏离原文。适用于需要发散思维或创意题目的场景。</li>
            </ul>
        </div></li>
    </ul>

    <h3>5. 最大Token数</h3>
    <ul>
        <li><strong>作用</strong>：限制API每次响应生成的最大文本长度（以token为单位）。Token是模型处理文本的基本单位，通常一个英文单词或一个汉字算作一个或几个token。</li>
        <li><div class="example"><strong>用法示例</strong>：如果您希望题目答案或解释简洁，可以设置较小的数值（如 <code>500</code>）。如果需要更详细的答案或长文本题目，可以设置较大的数值（如 <code>4000</code>）。设置过小可能导致题目不完整，过大可能增加API成本或处理时间。</div></li>
    </ul>

    <h3>6. 测试连接</h3>
    <ul>
        <li><strong>作用</strong>：点击此按钮可以测试您配置的API设置是否有效，以及是否能成功连接到大语言模型服务。</li>
        <li><div class="example"><strong>用法示例</strong>：在更改任何API设置后，建议点击“测试连接”按钮。如果显示“API连接测试成功！”，则表示您的配置正确；如果显示“API连接测试失败！”，则需要检查您的URL、密钥或网络连接。</div></li>
    </ul>

    <hr>

    <h2>二、 文本处理配置</h2>
    <p>此部分用于定义工具如何处理输入的文档，以优化题目生成过程。</p>

    <h3>1. 文本块大小 (tokens)</h3>
    <ul>
        <li><strong>作用</strong>：设置每个文本块的最大token数。文档在处理前会被分割成若干个文本块，每个文本块单独发送给API进行题目生成。此设置影响处理速度和生成题目的质量。</li>
        <li><div class="example"><strong>用法示例</strong>：
            <ul>
                <li>设置为 <code>1500</code>：较小的文本块，处理速度可能较快，但可能会丢失部分上下文信息，影响题目连贯性。</li>
                <li>设置为 <code>5000</code>：较大的文本块，能保留更多上下文，生成题目可能更全面和准确，但处理时间可能增加。</li>
            </ul>
        </div></li>
    </ul>

    <h3>2. 题目基础字数</h3>
    <ul>
        <li><strong>作用</strong>：设定生成一组题目所需的最少文本字数。</li>
        <li><div class="example"><strong>用法示例</strong>：如果您希望每组题目都基于相对充裕的文本内容生成，可以设置较高的基础字数（如 <code>2000</code>）。如果文本内容较短，可能需要降低此值以确保能生成题目。</div></li>
    </ul>

    <h3>3. 文档分割</h3>
    <ul>
        <li><strong>不进行文档分割</strong>：
            <ul>
                <li><strong>作用</strong>：勾选此选项后，工具将不对输入的整个文档进行分割，而是将整个文档作为一个整体发送给API生成题目。</li>
                <li><div class="example"><strong>用法示例</strong>：当您的文档内容较短（例如，一个简短的段落或几句话）且不希望进行分块处理时，可以勾选此选项。请注意，如果文档过长，不进行分割可能会超出API的输入限制。</div></li>
            </ul>
        </li>
    </ul>

    <h3>4. 分块逻辑</h3>
    <ul>
        <li><strong>使用新分块逻辑</strong>：
            <ul>
                <li><strong>作用</strong>：勾选此选项将启用更智能的文档分段方式。这种逻辑通常能生成无重叠的文本块，并根据文本内容和字数进行动态调整，以提高题目生成的质量和效率。</li>
                <li><div class="example"><strong>用法示例</strong>：对于大部分文档，推荐勾选此选项以获得更好的分块效果。如果发现题目生成效果不理想或有奇怪的分块现象，可以尝试取消勾选以使用默认分块逻辑。</div></li>
            </ul>
        </li>
    </ul>

    <h3>5. 预设配置按钮</h3>
    <ul>
        <li><strong>快速配置</strong>：
            <ul>
                <li><strong>作用</strong>：一键应用一组预设的配置，旨在实现快速生成题目。</li>
                <li><div class="example"><strong>用法示例</strong>：当您对题目质量要求不高，只希望快速得到一些题目作为参考时，可以点击此按钮。它会将文本块大小和题目基础字数设置为 <code>1500</code>，并生成较少数量的单选、多选、填空和判断题。</div></li>
            </ul>
        </li>
        <li><strong>平衡配置</strong>：
            <ul>
                <li><strong>作用</strong>：一键应用一组预设的配置，旨在平衡题目生成的速度和质量。</li>
                <li><div class="example"><strong>用法示例</strong>：对于大多数日常使用场景，推荐点击此按钮。它会将文本块大小和题目基础字数设置为 <code>2000</code>，并生成中等数量的单选、多选、填空、简答和判断题。</div></li>
            </ul>
        </li>
        <li><strong>精细配置</strong>：
            <ul>
                <li><strong>作用</strong>：一键应用一组预设的配置，旨在实现更高质量和更多样化的题目生成。</li>
                <li><div class="example"><strong>用法示例</strong>：当您需要生成高质量、数量较多的题目，并且不介意处理时间稍长时，可以点击此按钮。它会将文本块大小设置为 <code>5000</code>，题目基础字数设置为 <code>1300</code>，并生成较多数量的单选、多选、填空、简答和判断题。</div></li>
            </ul>
        </li>
    </ul>

    <hr>

    <h2>三、 题型数量配置</h2>
    <p>此部分允许您精确控制每个文本块或文档段落预期生成的各种题型的数量。</p>
<p>这个是前面的2. 题目基础字数 每个基础数字下的出题数目，如果最后字数是基础数字的两倍，则实际出题数目*2</p>
    <p><strong>说明</strong>：设置每个文本块生成各种题型的数量，总题目数量为各题型数量之和。新出题逻辑将根据文档字数和基础字数自动调整题目数量。基础字数是指生成一道题所需的最少文本字数。基础题数是指在满足基础字数条件下，每个文本块或文档段落预期生成的题目数量。</p>

    <h3>1. 单选题数量</h3>
    <ul>
        <li><strong>作用</strong>：设定每个文本块预期生成的单选题数量。</li>
        <li><div class="example"><strong>用法示例</strong>：如果您希望生成更多考察单一知识点的题目，可以将此值调高。例如，设置为 <code>4</code>，则每个文本块会尝试生成4道单选题。</div></li>
    </ul>

    <h3>2. 多选题数量</h3>
    <ul>
        <li><strong>作用</strong>：设定每个文本块预期生成的，可以有多个正确答案的多选题数量。</li>
        <li><div class="example"><strong>用法示例</strong>：如果文档内容适合考察多方面知识点，可以适当增加多选题数量。例如，设置为 <code>2</code>。</div></li>
    </ul>

    <h3>3. 填空题数量</h3>
    <ul>
        <li><strong>作用</strong>：设定每个文本块预期生成的填空题数量。</li>
        <li><div class="example"><strong>用法示例</strong>：当您需要考察用户对原文中关键信息的回忆和填充能力时，可以增加填空题数量。例如，设置为 <code>1</code>。</div></li>
    </ul>

    <h3>4. 简答题数量</h3>
    <ul>
        <li><strong>作用</strong>：设定每个文本块预期生成的需要简短回答的题目数量。</li>
        <li><div class="example"><strong>用法示例</strong>：当您希望考察用户对某个概念或事件的理解和解释能力时，可以增加简答题数量。例如，设置为 <code>1</code>。</div></li>
    </ul>

    <h3>5. 判断题数量</h3>
    <ul>
        <li><strong>作用</strong>：设定每个文本块预期生成的判断对错的题目数量。</li>
        <li><div class="example"><strong>用法示例</strong>：如果需要考察用户对事实性陈述的辨别能力，可以增加判断题数量。例如，设置为 <code>2</code>。</div></li>
    </ul>

    <h3>6. 排序题数量</h3>
    <ul>
        <li><strong>作用</strong>：设定每个文本块预期生成的需要排列顺序的题目数量。</li>
        <li><div class="example"><strong>用法示例</strong>：由于排序题通常较为复杂，默认数量为 <code>0</code>。如果文档内容包含明确的步骤、时间线或逻辑顺序，并且您需要生成此类题目，可以尝试增加此值（例如，设置为 <code>1</code>）。</div></li>
    </ul>

</body>
</html>