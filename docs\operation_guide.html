<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库系统操作指南 (流程图)</title>
    <script src="lib/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 20px auto;
            padding: 0 15px;
            background-color: #f9f9f9;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1 {
            text-align: center;
            color: #0056b3;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #007bff;
            margin-top: 30px;
        }
        .mermaid {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center; /* Center the flowchart */
            overflow-x: auto; /* Allow horizontal scrolling if chart is too wide */
        }
    </style>
</head>
<body>
    <h1>题库系统操作指南 (流程图)</h1>

    <h2>1. 生成CSV文件</h2>
    <div class="mermaid">
        graph TD
A[开始] --> B{主界面}
B --> C[选择题库源文件]
C --> D[左上角配置]
D --> E[设置输出路径]
E --> F[点击保存配置]
F --> G[点击生成题目]
G --> H[CSV文件生成]
H --> I[结束]
    </div>

    <h2>2. 转换题库格式</h2>
    <div class="mermaid">
        graph TD
J[开始] --> K[选择生成的CSV文件]
K --> L[选择目标格式]
L --> M[点击开始转换]
L --> N[转换完成]
N --> O[结束]
    </div>

    <h2>3. 常见问题</h2>
    <div class="mermaid">
        graph TD
P[开始] --> Q{遇到问题?}
Q -- 是 --> R[确保CSV符合模板格式]
Q -- 否 --> S[转换失败时查看logs/error.log]
R --> T[问题解决]
S --> T
T --> U[结束]
    </div>

    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>