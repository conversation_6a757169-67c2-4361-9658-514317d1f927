#!/usr/bin/env python3
"""
测试GUI界面修改
验证新融智和安培空间框架是否正确显示
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.main_application import MainApplication

def test_gui_layout():
    """测试GUI布局"""
    print("创建测试GUI窗口...")
    
    # 创建根窗口
    root = tk.Tk()
    root.title("GUI布局测试")
    root.geometry("800x600")
    
    # 创建主应用程序
    app = MainApplication(root)
    
    print("GUI窗口已创建，请检查以下内容：")
    print("1. 是否有'CSV转tdocx'标签页")
    print("2. 标签页中是否有'新融智'框架，包含'转换为TXT'和'转换为DOCX'按钮")
    print("3. 标签页中是否有'安培空间'框架，包含'转换为XLSX'按钮")
    print("4. 原有的主要操作和设置功能是否正常")
    
    # 运行GUI
    root.mainloop()

if __name__ == "__main__":
    test_gui_layout()
