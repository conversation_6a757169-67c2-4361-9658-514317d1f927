#!/usr/bin/env python3
"""
测试CSV转XLSX转换器
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from csv_to_tdocx.csv_to_xlsx_converter import CsvToXlsxConverter

def test_csv_to_xlsx():
    """测试CSV转XLSX功能"""
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建转换器
    converter = CsvToXlsxConverter()
    
    # 测试文件路径
    test_csv = "test/test_conversion.csv"
    output_dir = "test"
    
    print("开始测试CSV转XLSX转换...")
    print(f"输入文件: {test_csv}")
    print(f"输出目录: {output_dir}")
    
    # 执行转换
    result = converter.convert(test_csv, output_dir)
    
    print("\n转换结果:")
    print(f"成功: {result.get('success')}")
    print(f"消息: {result.get('message')}")
    
    if result.get('success'):
        print(f"输出文件: {result.get('output_path')}")
        print(f"行数: {result.get('row_count')}")
        print(f"列数: {result.get('column_count')}")
        print(f"编码: {result.get('input_encoding')}")
        print(f"文件大小: {result.get('file_size')} 字节")
        print(f"列名: {result.get('columns')}")
    else:
        print(f"错误: {result.get('errors')}")

if __name__ == "__main__":
    test_csv_to_xlsx()
