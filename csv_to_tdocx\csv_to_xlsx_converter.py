#!/usr/bin/env python3
"""
CSV转XLSX转换器
将CSV文件直接转换为Excel XLSX格式
"""

import os
import sys
import logging
import pandas as pd
from typing import Dict, Any

logger = logging.getLogger(__name__)

class CsvToXlsxConverter:
    """CSV转XLSX转换器"""
    
    def __init__(self):
        """初始化转换器"""
        self.logger = logging.getLogger(__name__)
    
    def convert(self, csv_file_path: str, output_dir: str) -> Dict[str, Any]:
        """
        将CSV文件转换为XLSX格式
        
        Args:
            csv_file_path: CSV输入文件路径
            output_dir: 输出目录
            
        Returns:
            转换结果字典
        """
        try:
            # 检查输入文件
            if not os.path.exists(csv_file_path):
                return {
                    "success": False,
                    "message": f"输入文件不存在: {csv_file_path}",
                    "errors": ["文件不存在"]
                }
            
            # 检查输出目录
            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                except Exception as e:
                    return {
                        "success": False,
                        "message": f"无法创建输出目录: {output_dir}",
                        "errors": [str(e)]
                    }
            
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(csv_file_path))[0]
            output_file_name = f"{base_name}.xlsx"
            output_file_path = os.path.join(output_dir, output_file_name)
            
            # 读取CSV文件
            try:
                # 尝试不同的编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                df = None
                used_encoding = None
                
                for encoding in encodings:
                    try:
                        df = pd.read_csv(csv_file_path, encoding=encoding)
                        used_encoding = encoding
                        self.logger.info(f"成功使用编码 {encoding} 读取CSV文件")
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        self.logger.warning(f"使用编码 {encoding} 读取失败: {e}")
                        continue
                
                if df is None:
                    return {
                        "success": False,
                        "message": "无法读取CSV文件，尝试了多种编码都失败",
                        "errors": ["编码错误"]
                    }
                
                # 检查数据
                if df.empty:
                    return {
                        "success": False,
                        "message": "CSV文件为空",
                        "errors": ["空文件"]
                    }
                
                # 转换为XLSX
                with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='Sheet1')
                
                # 验证输出文件
                if not os.path.exists(output_file_path):
                    return {
                        "success": False,
                        "message": "XLSX文件生成失败",
                        "errors": ["文件生成失败"]
                    }
                
                # 获取文件大小
                file_size = os.path.getsize(output_file_path)
                
                return {
                    "success": True,
                    "message": "CSV文件已成功转换为XLSX格式",
                    "output_path": output_file_path,
                    "input_encoding": used_encoding,
                    "row_count": len(df),
                    "column_count": len(df.columns),
                    "file_size": file_size,
                    "columns": list(df.columns)
                }
                
            except Exception as e:
                self.logger.error(f"读取CSV文件失败: {e}")
                return {
                    "success": False,
                    "message": f"读取CSV文件失败: {str(e)}",
                    "errors": [str(e)]
                }
                
        except Exception as e:
            self.logger.error(f"转换过程中发生错误: {e}")
            return {
                "success": False,
                "message": f"转换过程中发生错误: {str(e)}",
                "errors": [str(e)]
            }
    
    def validate_csv_file(self, csv_file_path: str) -> Dict[str, Any]:
        """
        验证CSV文件格式
        
        Args:
            csv_file_path: CSV文件路径
            
        Returns:
            验证结果
        """
        try:
            if not os.path.exists(csv_file_path):
                return {"valid": False, "error": "文件不存在"}
            
            # 尝试读取前几行
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file_path, encoding=encoding, nrows=5)
                    return {
                        "valid": True,
                        "encoding": encoding,
                        "columns": list(df.columns),
                        "sample_rows": len(df)
                    }
                except:
                    continue
            
            return {"valid": False, "error": "无法解析CSV文件"}
            
        except Exception as e:
            return {"valid": False, "error": str(e)}

def main():
    """测试函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CSV转XLSX转换器')
    parser.add_argument('input_file', help='输入CSV文件路径')
    parser.add_argument('output_dir', help='输出目录')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建转换器
    converter = CsvToXlsxConverter()
    
    # 执行转换
    result = converter.convert(args.input_file, args.output_dir)
    
    if result["success"]:
        print(f"转换成功！")
        print(f"输出文件: {result['output_path']}")
        print(f"行数: {result['row_count']}")
        print(f"列数: {result['column_count']}")
    else:
        print(f"转换失败: {result['message']}")

if __name__ == "__main__":
    main()
